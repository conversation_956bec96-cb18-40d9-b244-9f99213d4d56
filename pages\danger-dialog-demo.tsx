'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DangerConfirmDialog } from '@/components/ui/danger-confirm-dialog';
import { Trash2, AlertTriangle } from 'lucide-react';

export default function DangerDialogDemo() {
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const handleReset = () => {
    alert('重置操作已执行！（这只是演示）');
  };

  const handleDelete = () => {
    alert('删除操作已执行！（这只是演示）');
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          危险操作确认对话框演示
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-4">功能特性</h2>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <strong>3秒冷却期</strong> - 防止冲动操作
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <strong>备份提醒</strong> - 提醒用户备份重要数据
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                <strong>GitHub免责声明</strong> - 明确说明不会影响远程仓库
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                <strong>输入确认</strong> - 需要输入特定文本才能执行（不区分大小写）
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                <strong>详细后果说明</strong> - 清楚列出所有影响
              </li>
            </ul>
          </div>

          <div className="border-t pt-6">
            <h2 className="text-xl font-semibold mb-4">演示按钮</h2>
            <div className="flex gap-4">
              <Button
                variant="ghost"
                onClick={() => setResetDialogOpen(true)}
                className="hover:bg-red-50 hover:border-red-200 border border-transparent transition-all duration-200"
              >
                <Trash2 className="w-4 h-4 text-red-600 mr-2" />
                重置Git数据（完整版）
              </Button>

              <Button
                variant="ghost"
                onClick={() => setDeleteDialogOpen(true)}
                className="hover:bg-red-50 hover:border-red-200 border border-transparent transition-all duration-200"
              >
                <AlertTriangle className="w-4 h-4 text-red-600 mr-2" />
                删除项目（简化版）
              </Button>
            </div>
          </div>

          <div className="bg-gray-50 rounded-md p-4">
            <h3 className="font-medium text-gray-900 mb-2">使用说明：</h3>
            <ol className="text-sm text-gray-700 space-y-1">
              <li>1. 点击上方的危险操作按钮</li>
              <li>2. 仔细阅读警告信息和后果说明</li>
              <li>3. 注意备份提醒和GitHub免责声明</li>
              <li>4. 在输入框中输入要求的确认文本（可以是 reset、RESET、Reset 等）</li>
              <li>5. 等待3秒冷却期结束</li>
              <li>6. 点击确认按钮执行操作</li>
            </ol>
          </div>
        </div>

        {/* 重置Git数据对话框 - 完整功能版本 */}
        <DangerConfirmDialog
          open={resetDialogOpen}
          onOpenChange={setResetDialogOpen}
          title="重置Git数据"
          description="您即将重置所有Git数据，这是一个不可逆的危险操作。"
          consequences={[
            "清除所有本地文件状态",
            "删除所有本地提交历史记录",
            "重置所有本地分支信息",
            "清空本地存储数据",
            "页面将自动刷新"
          ]}
          confirmText="RESET"
          onConfirm={handleReset}
          actionLabel="确认重置"
          cooldownSeconds={3}
          showBackupReminder={true}
          showGitHubDisclaimer={true}
        />

        {/* 删除项目对话框 - 简化版本 */}
        <DangerConfirmDialog
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          title="删除项目"
          description="您即将永久删除此项目，此操作无法撤销。"
          consequences={[
            "删除所有项目文件",
            "清除项目配置",
            "移除相关数据"
          ]}
          confirmText="DELETE"
          onConfirm={handleDelete}
          actionLabel="确认删除"
          cooldownSeconds={3}
          showBackupReminder={true}
          showGitHubDisclaimer={false}
        />
      </div>
    </div>
  );
}

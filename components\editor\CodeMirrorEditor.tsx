'use client';

import React, { useEffect, useRef, useState } from 'react';
import { EditorView } from '@codemirror/view';
import { basicSetup } from 'codemirror';
import { EditorState } from '@codemirror/state';
import { markdown } from '@codemirror/lang-markdown';
import { oneDark } from '@codemirror/theme-one-dark';
import { useTheme } from 'next-themes';

interface CodeMirrorEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
}

export default function CodeMirrorEditor({
  value,
  onChange,
  placeholder = '开始编写您的 Markdown...',
  className = '',
  initialScrollPosition,
  onScrollPositionChange
}: CodeMirrorEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView | null>(null);
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !editorRef.current) return;

    // 创建编辑器状态
    const state = EditorState.create({
      doc: value,
      extensions: [
        basicSetup,
        markdown(),
        theme === 'dark' ? oneDark : [],
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            const newValue = update.state.doc.toString();
            onChange(newValue);
          }
          // 监听滚动变化
          if (update.viewportChanged && onScrollPositionChange) {
            const scrollTop = update.view.scrollDOM.scrollTop;
            onScrollPositionChange(scrollTop);
          }
        }),
        EditorView.theme({
          '&': {
            height: '100%',
            fontSize: '14px',
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
          },
          '.cm-content': {
            padding: '16px',
            minHeight: '100%',
            lineHeight: '1.6'
          },
          '.cm-focused': {
            outline: 'none'
          },
          '.cm-editor': {
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          },
          '.cm-scroller': {
            flex: '1',
            overflow: 'auto'
          }
        }),
        EditorView.lineWrapping
      ]
    });

    // 创建编辑器视图
    const view = new EditorView({
      state,
      parent: editorRef.current
    });

    viewRef.current = view;

    // 恢复滚动位置
    if (initialScrollPosition !== undefined) {
      setTimeout(() => {
        if (view.scrollDOM) {
          view.scrollDOM.scrollTop = initialScrollPosition;
        }
      }, 100);
    }

    return () => {
      view.destroy();
      viewRef.current = null;
    };
  }, [mounted, theme]);

  // 当外部value变化时更新编辑器内容
  useEffect(() => {
    if (viewRef.current && value !== viewRef.current.state.doc.toString()) {
      const transaction = viewRef.current.state.update({
        changes: {
          from: 0,
          to: viewRef.current.state.doc.length,
          insert: value
        }
      });
      viewRef.current.dispatch(transaction);
    }
  }, [value]);

  if (!mounted) {
    return (
      <div className={`w-full h-full flex items-center justify-center ${className}`}>
        <div className="text-muted-foreground">加载编辑器...</div>
      </div>
    );
  }

  return (
    <div 
      ref={editorRef} 
      className={`w-full h-full ${className}`}
    />
  );
}
